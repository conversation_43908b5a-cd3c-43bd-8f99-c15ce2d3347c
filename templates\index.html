<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتب دبي</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
            direction: ltr;
            font-size: 15px;
        }

        .main-content {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: calc(100vh - 70px);
            padding-top: 25px;
        }
        .container {
            background: white;
            padding: 30px 40px;
            border: 1px solid #e0e6ed;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            max-width: 850px;
            width: 100%;
            text-align: right;
        }

        .container > form {
            margin-top: 8px;
        }

        button.link-button {
            font-size: 12px;
            padding: 4px 10px;
            margin-bottom: 12px;
            width: auto;
            display: inline-block;
        }

        .form-section {
            margin-bottom: 20px;
            padding: 0 15px;
        }

        .form-row {
            display: flex;
            flex-direction: row-reverse;
            gap: 25px;
            margin-bottom: 12px;
            align-items: flex-start;
        }

        .form-group {
            flex: 1;
            direction: rtl;
        }

        label {
            text-align: right;
            display: block;
            direction: rtl;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 6px;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        .divider {
            border-bottom: 1px solid #e8e9ea;
            margin: 0px 0;
            width: 100%;
        }

        select {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: left 8px center;
            background-size: 12px;
            padding-left: 24px;
        }

        h2 {
            font-size: 18px;
            margin: 15px 0 10px;
            color: #2c3e50;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        .container .full-image {
            width: 100%; /* عرض الصورة ليأخذ كامل العرض */
            height: 100%; /* ارتفاع الصورة ليأخذ كامل الارتفاع */
            object-fit: cover; /* لضمان عدم تشويه الصورة */
            border-radius: 10px; /* يمكن إضافة بعض التناسق للصورة */
            margin-bottom: 20px; 
        }
        .container img {
            width:fit-content; /* عرض الصورة ليأخذ كامل العرض */
            height:fit-content; /* ارتفاع الصورة ليأخذ كامل الارتفاع */
            object-fit: cover; /* لضمان عدم تشويه الصورة */
            border-radius: 10px; /* يمكن إضافة بعض التناسق للصورة */
            margin-bottom: 20px; /* تباعد بين الصورة وبقية المحتوى */
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 36px;
            font-weight: 700;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }
        form {
            display: flex;
            flex-direction: column;
            width: 100%;
            margin: 0 auto;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .divider {
            border-bottom: 1px solid #e8e9ea;
            margin: 0px 0;
            width: 100%;
        }

        label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 6px;
            display: block;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        input, select {
            padding: 0px 14px;
            border: 1px solid #d1d9e0;
            border-radius: 6px;
            font-size: 14px;
            direction: rtl;
            text-align: right;
            height: 38px;
            width: 100%;
            box-sizing: border-box;
            background-color: #fff;
            transition: all 0.3s ease;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        /* أنماط textarea للملاحظات */
        textarea {
            padding: 8px 14px;
            border: 1px solid #d1d9e0;
            border-radius: 6px;
            font-size: 14px;
            direction: rtl;
            text-align: right;
            width: 100%;
            box-sizing: border-box;
            background-color: #fff;
            transition: all 0.3s ease;
            resize: none;
            min-height: 38px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.4;
        }

        textarea:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        button[type="submit"] {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 12px 40px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            margin: 25px auto;
            display: block;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        button[type="submit"]:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .camera-container {
            width: 60%;
            margin: 15px auto 25px;
            position: relative;
            height: 220px;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .capture-btn {
            margin: 12px auto;
            width: 180px;
            padding: 10px 18px;
            font-size: 14px;
            font-weight: 600;
            border-radius: 6px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
        }

        .capture-btn:hover {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }

        .form-section {
            margin-bottom: 25px;
        }



        select {
            background-position: left 12px center;
        }

        form > div {
            margin-bottom: 8px;
        }

        form > input:last-of-type {
            margin-bottom: 10px;
        }

        select {
            padding-right: 4px;
        }

        input:focus, select:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* تنسيقات الحقول المطلوبة - فقط عند التفاعل */
        input:required:invalid:not(:placeholder-shown), select:required:invalid:not(:placeholder-shown) {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        input:required:valid:not(:placeholder-shown), select:required:valid:not(:placeholder-shown) {
            border-color: #28a745;
        }

        /* إزالة التنسيق الأحمر من الحقول الفارغة عند التحميل */
        input:required:placeholder-shown, select:required:invalid:placeholder-shown {
            border-color: #ced4da;
            box-shadow: none;
        }

        .field-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        .required-field-label::after {
            content: " *";
            color: #dc3545;
            font-weight: bold;
        }

        h1 {
            font-size: 28px;
            margin: 15px 0;
        }
        button, a {
            padding: 10px 18px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            direction: rtl;
            text-align: center;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }
        button, a {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
        }
        button:hover, a:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
        }
        .photo-box {
            border: 1px solid #e0e6ed;
            width: 60%;
            height: 180px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px auto;
            position: relative;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .photo-box img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .cameras-section {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }

        .camera-group {
            flex: 1;
            max-width: 45%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .camera-container {
            width: 100%;
            height: 350px;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .camera-buttons {
            width: 100%;
            text-align: center;
            margin-top: 10px;
        }

        .photo-container {
            margin: 15px 0;
            position: relative;
        }

        .capture-btn {
            display: block;
            margin: 5px auto 20px;
            width: 160px;
            text-align: center;
            font-size: 13px;
            font-weight: 600;
            padding: 8px 14px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
        }

        .camera-buttons {
            text-align: center;
            margin-bottom: 15px;
        }

        button[type="submit"] {
            margin-top: 15px;
            font-size: 14px;
            padding: 10px 24px;
            width: auto;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .retake-btn {
            font-size: 12px;
            font-weight: 600;
            padding: 6px 14px;
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
            border: none;
            border-radius: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        video {
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
        }

        .photo-preview {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
        }

        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .retake-btn {
            z-index: 3;
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
            border: none;
            padding: 5px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        h2 {
            font-size: 15px;
            margin: 20px 0 8px;
            color: #2c3e50;
            clear: both;
        }

        .form-heading {
            font-size: 22px;
            color: #007bff;
            margin: 10px 0;
            text-align: right;
            padding: 0 15px;
            font-weight: 600;
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        form > h2:first-of-type {
            margin-top: 30px;
        }

        .amount-display {
            margin-top: 6px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 13px;
            color: #495057;
            direction: rtl;
            text-align: right;
            min-height: 22px;
        }

        .amount-display .formatted-number {
            font-weight: 600;
            color: #007bff;
            font-size: 14px;
        }

        .amount-display .arabic-text {
            margin-top: 3px;
            font-style: italic;
            color: #666;
        }



        /* إعادة تعيين تنسيقات التول بار لتجنب التداخل */
        .custom-toolbar {
            background: #ffffff !important;
            color: #2c3e50 !important;
        }

        .custom-toolbar .user-info {
            background: #f8f9fa !important;
            color: #2c3e50 !important;
            border: 1px solid #e9ecef !important;
        }

        .custom-toolbar .user-info:hover {
            background: #e9ecef !important;
            color: #2c3e50 !important;
        }

        .custom-toolbar .dropdown-item {
            background: transparent !important;
            color: #495057 !important;
        }

        .custom-toolbar .dropdown-item:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
            color: #2c3e50 !important;
        }

        .custom-toolbar .user-dropdown {
            background: white !important;
        }

        .custom-toolbar .office-logo {
            background: transparent !important;
            color: inherit !important;
        }

        .custom-toolbar .office-logo:hover {
            background: #f8f9fa !important;
            color: inherit !important;
        }

        /* تنسيقات إضافية للقائمة المنسدلة */
        .custom-toolbar .user-dropdown.show {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
            z-index: 9999 !important;
        }

        .custom-toolbar .user-info.active .dropdown-arrow {
            transform: rotate(180deg) !important;
        }

        .custom-toolbar .user-info {
            position: relative !important;
            z-index: 1000 !important;
        }

        .custom-toolbar .user-dropdown {
            position: absolute !important;
            top: 100% !important;
            right: 0 !important;
            z-index: 9999 !important;
        }

        /* أنماط عداد الأحرف والتحذيرات */
        .char-counter {
            font-size: 12px;
            margin-top: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            color: #6c757d;
            text-align: center;
            transition: all 0.3s ease;
        }

        .char-counter.safe {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .char-counter.warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .char-counter.danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
            font-weight: 600;
        }

        .char-warning {
            font-size: 11px;
            margin-top: 2px;
            padding: 3px 6px;
            border-radius: 3px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            text-align: center;
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .char-warning.show {
            display: block;
        }

        .char-warning.danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
            font-weight: 600;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* أنماط قسم البصمات */
        .fingerprint-section {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }

        .fingerprint-group {
            flex: 1;
            max-width: 45%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .fingerprint-container {
            width: 100%;
            height: 300px;
            border: 2px solid #e0e6ed;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .fingerprint-preview {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .fingerprint-canvas {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .fingerprint-status {
            margin-top: 15px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .fingerprint-status.waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .fingerprint-status.scanning {
            background: #cce5ff;
            color: #0066cc;
            border: 1px solid #99d6ff;
            animation: pulse 1.5s infinite;
        }

        .fingerprint-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .fingerprint-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .fingerprint-btn {
            display: block;
            margin: 10px auto;
            width: 200px;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            padding: 12px 20px;
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
            color: white;
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
            cursor: pointer;
        }

        .fingerprint-btn:hover {
            background: linear-gradient(135deg, #5a32a3, #4c2a85);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(111, 66, 193, 0.4);
        }

        .fingerprint-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .retry-fingerprint-btn {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .retry-fingerprint-btn:hover {
            background: linear-gradient(135deg, #e0a800, #d39e00);
            box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
        }

        .fingerprint-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .fingerprint-instructions {
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            margin-top: 10px;
            padding: 0 20px;
        }

    </style>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/toolbar.css') }}">
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    <div class="custom-toolbar">
        <div class="toolbar-container">
            <!-- الجانب الأيمن - معلومات المستخدم -->
            <div class="toolbar-right">
                <div class="user-info" onclick="toggleUserMenu()">
                    <div class="user-avatar">
                        {% if session.get('is_admin') %}
                            <span class="admin-icon">👑</span>
                        {% else %}
                            <span class="user-icon">👤</span>
                        {% endif %}
                    </div>
                    <div class="user-details">
                        <span class="user-name">{{ session.get('computer_name', 'مستخدم') }}</span>
                        <span class="user-type">
                            {% if session.get('is_admin') %}
                                إداري
                            {% else %}
                                مستخدم عادي
                            {% endif %}
                        </span>
                    </div>
                    <div class="dropdown-arrow">▼</div>
                </div>

                <!-- القائمة المنسدلة -->
                <div class="user-dropdown" id="userDropdown">
                    {% if session.get('is_admin') %}
                        <a href="/admin/dashboard" class="dropdown-item">
                            <span class="item-icon">🎛️</span>
                            لوحة التحكم
                        </a>
                        <a href="/computers" class="dropdown-item">
                            <span class="item-icon">💻</span>
                            قائمة الحاسبات
                        </a>
                        <a href="/admin/annual_export" class="dropdown-item">
                            <span class="item-icon">📊</span>
                            تحميل عقود السنة
                        </a>
                        <div class="dropdown-divider"></div>
                    {% endif %}
                    <a href="/" class="dropdown-item">
                        <span class="item-icon">📝</span>
                        إنشاء عقد جديد
                    </a>
                    <a href="/contracts" class="dropdown-item">
                        <span class="item-icon">📋</span>
                        عرض العقود المبرمة
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="/logout" class="dropdown-item logout-item">
                        <span class="item-icon">🚪</span>
                        تسجيل خروج
                    </a>
                </div>
            </div>

            <!-- الجانب الأيسر - شعار المكتب -->
            <div class="toolbar-left">
                <a href="/" class="office-logo">
                    <span class="logo-icon">🏢</span>
                    <span class="office-name">مكتب دبـي</span>
                </a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
        <img src="{{ url_for('static', filename='images/image_111.jpg') }}" alt="وصف الصورة" class="full-image" >

        <h1>نموذج تعبئة عقد بيع وشراء السيارات</h1>

        <!-- قسم الرقم التسلسلي الحالي -->
        <div id="serial-status" style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 18px; border-radius: 10px; margin-bottom: 25px; border: 1px solid #dee2e6; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
            <h3 style="margin: 0 0 12px 0; color: #2c3e50; font-weight: 600;">الرقم التسلسلي الحالي</h3>
            <div style="font-size: 26px; font-weight: 700; color: #007bff;">
                <span id="current-serial-display">جاري التحميل...</span>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div style="margin-bottom: 15px; text-align: center;">
            <button id="load-saved-btn" class="link-button" style="display: none;" onclick="loadSavedContract()">تحميل العقد المحفوظ</button>
            <button onclick="window.location.href='/contracts'" class="contracts-btn" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); text-decoration: none; display: inline-block;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(40, 167, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.3)'">📋 عرض العقود المبرمة</button>
        </div>

            {% if session.get('is_admin') %}
            <!-- زر تعديل الرقم التسلسلي للإدارة فقط -->
            <div class="admin-serial-section" style="margin: 15px 0; text-align: center;">
                <button onclick="window.location.href='/new_page'" class="admin-serial-btn" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; border: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3); text-decoration: none; display: inline-block;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(255, 107, 107, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(255, 107, 107, 0.3)'">📝 تعديل الرقم التسلسلي</button>
            </div>
            {% endif %}
        
        
        <form action="/" method="POST" onsubmit="return validateForm()" novalidate>
            <h2 class="form-heading">بيانات المالك الشرعي</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_1" class="required-field-label">اسم المالك الشرعي:</label>
                        <input type="text" name="name_1" id="name_1" required dir="rtl" placeholder="أدخل اسم المالك الشرعي">
                    </div>
                    <div class="form-group">
                        <label for="location_1" class="required-field-label">عنوان المالك الشرعي:</label>
                        <input type="text" name="location_1" id="location_1" required dir="rtl" placeholder="أدخل عنوان المالك الشرعي">
                    </div>
                </div>
            </div>
            <div class="divider"></div>

            <h2 class="form-heading">بيانات البائع</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_2">اسم البائع:</label>
                        <input type="text" name="name_2" id="name_2" required dir="rtl" placeholder="أدخل اسم البائع">
                    </div>
                    <div class="form-group">
                        <label for="location_2">عنوان البائع:</label>
                        <input type="text" name="location_2" id="location_2" required dir="rtl" placeholder="أدخل عنوان البائع">
                    </div>
                    <div class="form-group">
                        <label for="id_1">رقم الهوية للبائع:</label>
                        <input type="text" name="id_1" id="id_1" required dir="rtl" placeholder="أدخل رقم الهوية">
                    </div>
                    <div class="form-group">
                        <label for="phone_1">رقم الهاتف للبائع:</label>
                        <input type="text" name="phone_1" id="phone_1" required maxlength="11" pattern="[0-9]{11}" title="يجب أن يكون رقم الهاتف 11 رقم بالضبط" dir="rtl" placeholder="أدخل رقم الهاتف (11 رقم)">
                        <div id="phone_1_warning" style="color: red; font-size: 14px; display: none;">يجب أن يكون رقم الهاتف 11 رقم بالضبط</div>
                    </div>
                </div>
            </div>
            <div class="divider"></div>

            <h2 class="form-heading">بيانات المشتري</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_3">اسم المشتري:</label>
                        <input type="text" name="name_3" id="name_3" required dir="rtl" placeholder="أدخل اسم المشتري">
                    </div>
                    <div class="form-group">
                        <label for="location_3">عنوان المشتري:</label>
                        <input type="text" name="location_3" id="location_3" required dir="rtl" placeholder="أدخل عنوان المشتري">
                    </div>
                    <div class="form-group">
                        <label for="id_2">رقم هوية المشتري:</label>
                        <input type="text" name="id_2" id="id_2" required dir="rtl" placeholder="أدخل رقم الهوية">
                    </div>
                    <div class="form-group">
                        <label for="phone_2">رقم الهاتف للمشتري:</label>
                        <input type="text" name="phone_2" id="phone_2" required maxlength="11" pattern="[0-9]{11}" title="يجب أن يكون رقم الهاتف 11 رقم بالضبط" dir="rtl" placeholder="أدخل رقم الهاتف (11 رقم)">
                        <div id="phone_2_warning" style="color: red; font-size: 14px; display: none;">يجب أن يكون رقم الهاتف 11 رقم بالضبط</div>
                    </div>
                </div>
            </div>
            <div class="divider"></div>

            <h2 class="form-heading">بيانات السيارة</h2>
            <div class="form-section">
                <!-- Car details row 1 - نوع السيارة فقط -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="car_type">نوع السيارة:</label>
                        <input type="text" name="car_type" id="car_type" required dir="rtl" placeholder="أدخل نوع السيارة">
                    </div>
                </div>

                <!-- Car details row 2 - رقم السيارة + رمز اللوحة + المدينة + صنف المركبة -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="car_num_input">رقم السيارة:</label>
                        <input type="text" name="car_num_input" id="car_num_input" required dir="rtl" placeholder="أدخل رقم السيارة">
                    </div>
                    <div class="form-group">
                        <label for="car_code">رمز اللوحة:</label>
                        <select name="car_code" id="car_code" required>
                            <option value="">اختر رمز اللوحة</option>
                            <option value="أ-A">أ-A</option>
                            <option value="ب-B">ب-B</option>
                            <option value="ج-J">ج-J</option>
                            <option value="د-D">د-D</option>
                            <option value="هـ-H">هـ-H</option>
                            <option value="ي-E">ي-E</option>
                            <option value="ف-F">ف-F</option>
                            <option value="س-S">س-S</option>
                            <option value="ر-R">ر-R</option>
                            <option value="ن-N">ن-N</option>
                            <option value="م-M">م-M</option>
                            <option value="و-W">و-W</option>
                            <option value="ك-K">ك-K</option>
                            <option value="ق-Q">ق-Q</option>
                            <option value="ل-L">ل-L</option>
                            <option value="ز-Z">ز-Z</option>
                            <option value="ط-T">ط-T</option>
                            <option value="بلا">بلا</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="car_city">المدينة:</label>
                        <select name="car_city" id="car_city" required>
                            <option value="">اختر المدينة</option>
                            <option value="بغداد">بغداد</option>
                            <option value="نينوى">نينوى</option>
                            <option value="ميسان">ميسان</option>
                            <option value="البصرة">البصرة</option>
                            <option value="الأنبار">الأنبار</option>
                            <option value="القادسية">القادسية</option>
                            <option value="المثنى">المثنى</option>
                            <option value="بابل">بابل</option>
                            <option value="كربلاء">كربلاء</option>
                            <option value="ديالى">ديالى</option>
                            <option value="السليمانية">السليمانية</option>
                            <option value="أربيل">أربيل</option>
                            <option value="حلبجة">حلبجة</option>
                            <option value="دهوك">دهوك</option>
                            <option value="كركوك">كركوك</option>
                            <option value="صلاح الدين">صلاح الدين</option>
                            <option value="ذي قار">ذي قار</option>
                            <option value="النجف">النجف</option>
                            <option value="واسط">واسط</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="car_prop">صنف المركبة:</label>
                        <select name="car_prop" id="car_prop" required>
                            <option value="">اختر صنف المركبة</option>
                            <option value="خصوصي">خصوصي</option>
                            <option value="اجرة">اجرة</option>
                            <option value="حمل">حمل</option>
                            <option value="انشائية">انشائية</option>
                            <option value="دراجة نارية">دراجة نارية</option>
                        </select>
                    </div>
                </div>

                <!-- Car details row 3 - لون السيارة + موديل السيارة + رقم الشاصي + رقم السنوية -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="car_colar">لون السيارة:</label>
                        <input type="text" name="car_colar" id="car_colar" required dir="rtl" placeholder="أدخل لون السيارة">
                    </div>
                    <div class="form-group">
                        <label for="car_model">موديل السيارة:</label>
                        <input type="text" name="car_model" id="car_model" required dir="rtl" placeholder="أدخل موديل السيارة">
                    </div>
                    <div class="form-group">
                        <label for="sasi_num">رقم الشاصي:</label>
                        <input type="text" name="sasi_num" id="sasi_num" required dir="rtl" placeholder="أدخل رقم الشاصي">
                    </div>
                    <div class="form-group">
                        <label for="sin_num">رقم السنوية:</label>
                        <input type="text" name="sin_num" id="sin_num" required dir="rtl" placeholder="أدخل رقم السنوية">
                    </div>
                </div>
            </div>
            <div class="divider"></div>

            <h2 class="form-heading">تفاصيل المبالغ المالية</h2>
            <div class="form-section">
                <!-- قائمة نوع العملة -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="currency_type">نوع العملة:</label>
                        <select name="currency_type" id="currency_type" required>
                            <option value="">اختر نوع العملة</option>
                            <option value="IQD">دينار عراقي</option>
                            <option value="USD">دولار أمريكي</option>
                        </select>
                    </div>
                </div>

                <!-- الصف الثاني - حقول المبالغ المالية -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="badal_num">سعر السيارة :</label>
                        <input type="text" name="badal_num" id="badal_num" required dir="rtl" placeholder="أدخل قيمة البيع">
                        <div id="badal_num_display" class="amount-display"></div>
                    </div>
                    <div class="form-group">
                        <label for="mony_num">المبلغ الواصل :</label>
                        <input type="text" name="mony_num" id="mony_num" required dir="rtl" placeholder="أدخل المبلغ المقبوض">
                        <div id="mony_num_display" class="amount-display"></div>
                    </div>
                    <div class="form-group">
                        <label for="mony_not_delevired_display_field">المبلغ الباقي :</label>
                        <input type="text" id="mony_not_delevired_display_field" readonly style="background-color: #f8f9fa; cursor: not-allowed;">
                        <div id="mony_not_delevired_display" class="amount-display"></div>
                    </div>
                </div>
                <!-- الصف الثالث - ملاحظات الباقي (يظهر عند الحاجة) -->
                <div class="form-row" id="remaining_notes_row" style="display: none;">
                    <div class="form-group">
                        <label for="m1">ملاحظات الباقي:</label>
                        <textarea name="m1" id="m1" dir="rtl" placeholder="أدخل ملاحظات حول المبلغ المتبقي..." rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="divider"></div>



            <h2 class="form-heading">الملاحظات</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="note_a">ملاحظة أ:</label>
                        <textarea name="note_a" id="note_a" placeholder="أدخل ملاحظة أ..." rows="1"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="note_b">ملاحظة ب:</label>
                        <textarea name="note_b" id="note_b" placeholder="أدخل ملاحظة ب..." rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="divider"></div>

            <h2 class="form-heading">بصمات الأطراف</h2>

            <!-- زر اختبار الاتصال -->
            <div style="text-align: center; margin-bottom: 20px;">
                <button type="button" id="test-connection-btn" onclick="testFingerprintConnection()"
                        style="background: linear-gradient(135deg, #17a2b8, #138496); color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer; margin-right: 10px;">
                    🔌 اختبار الاتصال بالمستشعر
                </button>
                <span id="connection-status" style="font-size: 14px; margin-left: 10px;"></span>
            </div>

            <div class="fingerprint-section">
                <div class="fingerprint-group">
                    <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 16px;">بصمة البائع</h3>
                    <div class="fingerprint-container">
                        <div class="fingerprint-preview" id="seller-fingerprint-preview">
                            <div class="fingerprint-icon">👆</div>
                            <div class="fingerprint-status waiting" id="seller-fingerprint-status">
                                في انتظار بدء الاستشعار
                            </div>
                            <canvas id="seller-fingerprint-canvas" class="fingerprint-canvas" width="200" height="200" style="display: none;"></canvas>
                        </div>
                    </div>

                    <button type="button" id="start-seller-fingerprint" onclick="startFingerprintScan('seller')" class="fingerprint-btn">
                        🔍 بدء استشعار بصمة البائع
                    </button>
                    <button type="button" id="retry-seller-fingerprint" onclick="retryFingerprintScan('seller')" class="fingerprint-btn retry-fingerprint-btn" style="display: none;">
                        🔄 إعادة المحاولة
                    </button>
                </div>

                <div class="fingerprint-group">
                    <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 16px;">بصمة المشتري</h3>
                    <div class="fingerprint-container">
                        <div class="fingerprint-preview" id="buyer-fingerprint-preview">
                            <div class="fingerprint-icon">👆</div>
                            <div class="fingerprint-status waiting" id="buyer-fingerprint-status">
                                في انتظار بدء الاستشعار
                            </div>
                            <canvas id="buyer-fingerprint-canvas" class="fingerprint-canvas" width="200" height="200" style="display: none;"></canvas>
                        </div>
                    </div>

                    <button type="button" id="start-buyer-fingerprint" onclick="startFingerprintScan('buyer')" class="fingerprint-btn">
                        🔍 بدء استشعار بصمة المشتري
                    </button>
                    <button type="button" id="retry-buyer-fingerprint" onclick="retryFingerprintScan('buyer')" class="fingerprint-btn retry-fingerprint-btn" style="display: none;">
                        🔄 إعادة المحاولة
                    </button>
                </div>
            </div>
            <div class="divider"></div>

            <input type="hidden" name="buyerPhoto" id="buyerPhotoInput">
            <input type="hidden" name="sellerPhoto" id="sellerPhotoInput">
            <input type="hidden" name="sellerFingerprint" id="sellerFingerprintInput">
            <input type="hidden" name="buyerFingerprint" id="buyerFingerprintInput">
            <input type="hidden" name="mony_not_delevired" id="mony_not_delevired">
            <input type="hidden" name="is_editing_saved" id="is_editing_saved" value="false">
            <input type="hidden" name="local_serial_number" id="local_serial_number" value="">

            <input type="hidden" name="car_num" id="car_num" value="">
            <!-- حقول التاريخ والوقت التلقائية -->
            <input type="hidden" name="t" id="t" value="">
            <input type="hidden" name="t_1" id="t_1" value="">
            <input type="hidden" name="day" id="day" value="">



            <div class="cameras-section">
                <div class="camera-group">
                    <h2 class="form-heading">التقاط صورة البائع</h2>
                    <div class="camera-container">
                        <video id="video-buyer" autoplay></video>
                        <div class="photo-preview" id="photo-box-buyer" style="display: none;"></div>
                    </div>
                    <div class="camera-buttons">
                        <button type="button" id="capture-btn-buyer" onclick="capturePhoto('buyer')" class="capture-btn">التقاط صورة البائع</button>
                        <button type="button" id="retake-btn-buyer" onclick="retakePhoto('buyer')" class="capture-btn" style="display: none;">إعادة التقاط الصورة</button>
                    </div>
                </div>

                <div class="camera-group">
                    <h2 class="form-heading">التقاط صورة المشتري</h2>
                    <div class="camera-container">
                        <video id="video-seller" autoplay></video>
                        <div class="photo-preview" id="photo-box-seller" style="display: none;"></div>
                    </div>
                    <div class="camera-buttons">
                        <button type="button" id="capture-btn-seller" onclick="capturePhoto('seller')" class="capture-btn">التقاط صورة المشتري</button>
                        <button type="button" id="retake-btn-seller" onclick="retakePhoto('seller')" class="capture-btn" style="display: none;">إعادة التقاط الصورة</button>
                    </div>
                </div>
            </div>

            <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap; margin-top: 25px;">
                <button type="button" onclick="finalizeContract()" style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 14px 28px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 123, 255, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 123, 255, 0.3)'">إبرام العقد</button>
            </div>
        </form>
        
    </div>
    <script>
        // دالة تبديل قائمة المستخدم للتول بار
        function toggleUserMenu() {
            console.log('toggleUserMenu called'); // للتشخيص
            const dropdown = document.getElementById('userDropdown');
            const userInfo = document.querySelector('.user-info');

            console.log('dropdown:', dropdown); // للتشخيص
            console.log('userInfo:', userInfo); // للتشخيص

            if (dropdown && userInfo) {
                dropdown.classList.toggle('show');
                userInfo.classList.toggle('active');
                console.log('Classes toggled'); // للتشخيص
                console.log('dropdown classes:', dropdown.className); // للتشخيص
            } else {
                console.log('Elements not found!'); // للتشخيص
            }
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const userInfo = document.querySelector('.user-info');
            const dropdown = document.getElementById('userDropdown');

            if (userInfo && dropdown && !userInfo.contains(event.target)) {
                dropdown.classList.remove('show');
                userInfo.classList.remove('active');
            }
        });

        window.onload = function() {
            window.print();
        };
    </script>
    <script>
        let buyerPhoto = null;
        let sellerPhoto = null;
        let buyerStream = null;
        let sellerStream = null;

        // متغيرات البصمات - نظام احترافي
        let sellerFingerprint = null;
        let buyerFingerprint = null;
        let fingerprintPort = null;
        let isScanning = false;
        let deviceInfo = null;
        let connectionMonitor = null;
        let sensorProtocol = null;

        // معلومات المستشعرات المدعومة
        const SENSOR_PROTOCOLS = {
            ZKTECO: {
                name: 'ZKTeco',
                baudRate: 115200,
                initCommand: [0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x01, 0x00, 0x05],
                scanCommand: [0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x02, 0x00, 0x06],
                imageCommand: [0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x0A, 0x00, 0x0E],
                imageSize: { width: 256, height: 288 }
            },
            FUTRONIC: {
                name: 'Futronic',
                baudRate: 9600,
                initCommand: [0x5A, 0xA5, 0x01, 0x00, 0x00, 0x00, 0x01],
                scanCommand: [0x5A, 0xA5, 0x02, 0x00, 0x00, 0x00, 0x02],
                imageCommand: [0x5A, 0xA5, 0x03, 0x00, 0x00, 0x00, 0x03],
                imageSize: { width: 320, height: 480 }
            },
            DIGITAL_PERSONA: {
                name: 'Digital Persona',
                baudRate: 57600,
                initCommand: [0x02, 0x01, 0x00, 0x00, 0x00, 0x03],
                scanCommand: [0x02, 0x02, 0x00, 0x00, 0x00, 0x04],
                imageCommand: [0x02, 0x03, 0x00, 0x00, 0x00, 0x05],
                imageSize: { width: 355, height: 391 }
            },
            GENERIC: {
                name: 'Generic',
                baudRate: 9600,
                initCommand: [0x01, 0x00, 0x00, 0x00, 0x01],
                scanCommand: [0x02, 0x00, 0x00, 0x00, 0x02],
                imageCommand: [0x03, 0x00, 0x00, 0x00, 0x03],
                imageSize: { width: 256, height: 256 }
            }
        };



        // دالة دمج رقم السيارة مع رمز اللوحة
        function updateCarNumber() {
            const carNumInput = document.getElementById('car_num_input');
            const carCodeSelect = document.getElementById('car_code');
            const carNumHidden = document.getElementById('car_num');

            const carNumber = carNumInput.value.trim();
            const carCode = carCodeSelect.value;

            if (carNumber && carCode && carCode !== 'بلا') {
                // إذا كان هناك رقم ورمز (وليس "بلا")، ادمجهما
                carNumHidden.value = carNumber + ' ' + carCode;
            } else if (carNumber) {
                // إذا كان هناك رقم فقط أو اختار "بلا"، اعرض الرقم فقط
                carNumHidden.value = carNumber;
            } else {
                carNumHidden.value = '';
            }
        }

        // دالة ملء التاريخ والوقت تلقائياً بالتوقيت العراقي/السعودي
        function setAutoDateTime() {
            // إنشاء تاريخ جديد بالتوقيت العراقي (UTC+3)
            const now = new Date();
            const iraqTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3

            // الحصول على الساعة والدقيقة
            const hours = iraqTime.getUTCHours();
            const minutes = iraqTime.getUTCMinutes();

            // تنسيق الوقت (8:00)
            const timeString = hours + ':' + (minutes < 10 ? '0' + minutes : minutes);

            // تحديد الفترة (صباحاً أو مساءً)
            const period = hours < 12 ? 'صباحا' : 'مساءا';

            // أسماء الأيام بالعربية
            const arabicDays = ['الاحد', 'الاثنين', 'الثلاثاء', 'الاربعاء', 'الخميس', 'الجمعة', 'السبت'];
            const dayName = arabicDays[iraqTime.getUTCDay()];

            // ملء الحقول المخفية
            document.getElementById('t').value = timeString;
            document.getElementById('t_1').value = period;
            document.getElementById('day').value = dayName;
        }

        // إضافة مستمعات الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // ملء التاريخ والوقت تلقائياً
            setAutoDateTime();

            const carNumInput = document.getElementById('car_num_input');
            const carCodeSelect = document.getElementById('car_code');

            if (carNumInput) {
                carNumInput.addEventListener('input', updateCarNumber);
            }

            if (carCodeSelect) {
                carCodeSelect.addEventListener('change', updateCarNumber);
            }
        });

        function startVideo(type) {
            if (type === 'buyer') {
                navigator.mediaDevices.getUserMedia({ video: true })
                    .then(function (stream) {
                        buyerStream = stream;
                        const video = document.getElementById('video-buyer');
                        video.srcObject = stream;
                    })
                    .catch(function (err) {
                        alert("حدث خطأ في الوصول إلى الكاميرا: " + err);
                    });
            }

            if (type === 'seller') {
                navigator.mediaDevices.getUserMedia({ video: true })
                    .then(function (stream) {
                        sellerStream = stream;
                        const video = document.getElementById('video-seller');
                        video.srcObject = stream;
                    })
                    .catch(function (err) {
                        alert("حدث خطأ في الوصول إلى الكاميرا: " + err);
                    });
            }
        }

        function capturePhoto(type) {
            let videoElement, photoBox, captureBtn;
            let canvas = document.createElement('canvas');
            let ctx = canvas.getContext('2d');

            if (type === 'buyer') {
                videoElement = document.getElementById('video-buyer');
                photoBox = document.getElementById('photo-box-buyer');
                captureBtn = document.getElementById('capture-btn-buyer');
            } else if (type === 'seller') {
                videoElement = document.getElementById('video-seller');
                photoBox = document.getElementById('photo-box-seller');
                captureBtn = document.getElementById('capture-btn-seller');
            }

            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            let photoData = canvas.toDataURL('image/jpeg').split(',')[1];

            // Hide video and show photo preview
            videoElement.style.display = 'none';
            photoBox.style.display = 'block';

            let photoHtml = `<img src="data:image/jpeg;base64,${photoData}" alt="${type} Photo">`;
            photoBox.innerHTML = photoHtml;

            // Hide capture button and show retake button
            captureBtn.style.display = 'none';
            document.getElementById(`retake-btn-${type}`).style.display = 'inline-block';

            if (type === 'buyer') {
                buyerPhoto = photoData;
                document.getElementById('buyerPhotoInput').value = photoData;
            } else if (type === 'seller') {
                sellerPhoto = photoData;
                document.getElementById('sellerPhotoInput').value = photoData;
            }

            // Stop the camera stream
            if (type === 'buyer') {
                buyerStream.getTracks().forEach(track => track.stop());
            } else if (type === 'seller') {
                sellerStream.getTracks().forEach(track => track.stop());
            }
        }

        function retakePhoto(type) {
            let videoElement = document.getElementById(`video-${type}`);
            let photoBox = document.getElementById(`photo-box-${type}`);
            let captureBtn = document.getElementById(`capture-btn-${type}`);

            // Show video and hide photo preview
            videoElement.style.display = 'block';
            photoBox.style.display = 'none';
            captureBtn.style.display = 'block';
            document.getElementById(`retake-btn-${type}`).style.display = 'none';

            // Restart video stream
            startVideo(type);
        }

        // نظام الاتصال الاحترافي بمستشعر البصمة
        async function connectFingerprintSensor(forceNewConnection = false) {
            try {
                if (!navigator.serial) {
                    throw new Error('متصفحك لا يدعم Web Serial API. استخدم Chrome 89+ أو Edge 89+');
                }

                // التحقق من وجود اتصال نشط
                if (!forceNewConnection && fingerprintPort && sensorProtocol) {
                    console.log('استخدام الاتصال النشط');
                    return true;
                }

                // طلب اختيار الجهاز فقط عند الحاجة
                if (!fingerprintPort || forceNewConnection) {
                    fingerprintPort = await navigator.serial.requestPort();

                    // حفظ معلومات الجهاز
                    const portInfo = fingerprintPort.getInfo();
                    deviceInfo = {
                        vendorId: portInfo.usbVendorId,
                        productId: portInfo.usbProductId,
                        timestamp: Date.now()
                    };
                    localStorage.setItem('fingerprintDevice', JSON.stringify(deviceInfo));
                    console.log('تم حفظ معلومات الجهاز:', deviceInfo);
                }

                // كشف نوع المستشعر والاتصال
                sensorProtocol = await detectSensorType();
                if (!sensorProtocol) {
                    // استخدام بروتوكول افتراضي إذا فشل الكشف
                    console.log('فشل كشف المستشعر، استخدام البروتوكول الافتراضي');
                    sensorProtocol = SENSOR_PROTOCOLS.GENERIC;
                }

                console.log(`تم كشف/تعيين مستشعر: ${sensorProtocol.name}`);

                // بدء مراقبة الاتصال
                startConnectionMonitoring();

                return true;
            } catch (error) {
                console.error('خطأ في الاتصال:', error);

                if (error.name === 'NotFoundError') {
                    throw new Error('لم يتم اختيار أي جهاز');
                }

                throw error;
            }
        }

        // كشف نوع المستشعر تلقائياً - محسن
        async function detectSensorType() {
            const protocols = Object.values(SENSOR_PROTOCOLS);

            for (const protocol of protocols) {
                try {
                    console.log(`جاري اختبار بروتوكول: ${protocol.name}`);

                    // محاولة الاتصال بمعدل البيانات المحدد
                    if (fingerprintPort.readable) {
                        try {
                            await fingerprintPort.close();
                            // انتظار قصير للتأكد من إغلاق الاتصال
                            await new Promise(resolve => setTimeout(resolve, 100));
                        } catch (closeError) {
                            console.log('تجاهل خطأ الإغلاق:', closeError.message);
                        }
                    }

                    await fingerprintPort.open({
                        baudRate: protocol.baudRate,
                        dataBits: 8,
                        stopBits: 1,
                        parity: 'none',
                        flowControl: 'none'
                    });

                    // انتظار قصير للتأكد من استقرار الاتصال
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // اختبار الأمر الأولي
                    const response = await testProtocolCommand(protocol.initCommand);
                    if (response) {
                        console.log(`تم كشف المستشعر: ${protocol.name}`);
                        return protocol;
                    }

                } catch (error) {
                    console.log(`فشل اختبار ${protocol.name}:`, error.message);
                    // الاستمرار في المحاولة مع البروتوكول التالي
                }
            }

            // إذا فشل كل شيء، محاولة أخيرة مع البروتوكول العام
            try {
                console.log('محاولة أخيرة مع البروتوكول العام');
                const genericProtocol = SENSOR_PROTOCOLS.GENERIC;

                if (fingerprintPort.readable) {
                    await fingerprintPort.close();
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                await fingerprintPort.open({
                    baudRate: genericProtocol.baudRate,
                    dataBits: 8,
                    stopBits: 1,
                    parity: 'none',
                    flowControl: 'none'
                });

                console.log('تم الاتصال بالبروتوكول العام');
                return genericProtocol;

            } catch (error) {
                console.error('فشل في جميع المحاولات:', error);
                return null;
            }
        }

        // اختبار أمر البروتوكول - محسن
        async function testProtocolCommand(command) {
            let writer = null;
            let reader = null;

            try {
                writer = fingerprintPort.writable.getWriter();
                reader = fingerprintPort.readable.getReader();

                // إرسال الأمر
                await writer.write(new Uint8Array(command));
                writer.releaseLock();
                writer = null;

                // انتظار الرد لمدة 1 ثانية (أقصر للسرعة)
                const timeout = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('timeout')), 1000)
                );

                const readPromise = reader.read();
                const result = await Promise.race([readPromise, timeout]);

                reader.releaseLock();
                reader = null;

                // التحقق من وجود رد صالح
                if (result.value && result.value.length > 0) {
                    console.log('رد المستشعر:', Array.from(result.value.slice(0, 5)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
                    return true;
                }

                return false;
            } catch (error) {
                console.log('خطأ في اختبار الأمر:', error.message);
                return false;
            } finally {
                // تنظيف الموارد
                try {
                    if (writer) writer.releaseLock();
                } catch {}
                try {
                    if (reader) reader.releaseLock();
                } catch {}
            }
        }

        // مراقبة حالة الاتصال
        function startConnectionMonitoring() {
            if (connectionMonitor) {
                clearInterval(connectionMonitor);
            }

            connectionMonitor = setInterval(async () => {
                try {
                    if (!fingerprintPort || !fingerprintPort.readable) {
                        throw new Error('الجهاز غير متصل');
                    }

                    // إرسال ping للتحقق من الاتصال
                    const pingCommand = sensorProtocol.initCommand;
                    const writer = fingerprintPort.writable.getWriter();
                    await writer.write(new Uint8Array(pingCommand));
                    writer.releaseLock();

                    // تحديث حالة الاتصال
                    updateConnectionStatus('متصل', 'success');

                } catch (error) {
                    console.error('فقدان الاتصال:', error);
                    updateConnectionStatus('منقطع', 'error');

                    // محاولة إعادة الاتصال
                    setTimeout(attemptReconnection, 2000);
                }
            }, 5000); // فحص كل 5 ثوان
        }

        // محاولة إعادة الاتصال
        async function attemptReconnection() {
            try {
                updateConnectionStatus('جاري إعادة الاتصال...', 'warning');

                const savedDevice = localStorage.getItem('fingerprintDevice');
                if (savedDevice) {
                    // محاولة إعادة الاتصال بالجهاز المحفوظ
                    await connectFingerprintSensor();
                    updateConnectionStatus('تم إعادة الاتصال', 'success');
                } else {
                    updateConnectionStatus('يتطلب إعادة تعريف الجهاز', 'error');
                }
            } catch (error) {
                updateConnectionStatus('فشل في إعادة الاتصال', 'error');
            }
        }

        // تحديث حالة الاتصال في الواجهة
        function updateConnectionStatus(message, type) {
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                const colors = {
                    success: '#28a745',
                    warning: '#ffc107',
                    error: '#dc3545'
                };

                statusElement.innerHTML = `<span style="color: ${colors[type]};">${message}</span>`;
            }
        }

        // بدء استشعار البصمة - النظام الاحترافي
        async function startFingerprintScan(type) {
            if (isScanning) {
                alert('جاري استشعار بصمة أخرى، يرجى الانتظار');
                return;
            }

            const statusElement = document.getElementById(`${type}-fingerprint-status`);
            const startButton = document.getElementById(`start-${type}-fingerprint`);
            const retryButton = document.getElementById(`retry-${type}-fingerprint`);
            const canvas = document.getElementById(`${type}-fingerprint-canvas`);
            const iconElement = document.querySelector(`#${type}-fingerprint-preview .fingerprint-icon`);

            try {
                isScanning = true;
                startButton.disabled = true;

                // التحقق من الاتصال
                if (!fingerprintPort || !sensorProtocol) {
                    statusElement.textContent = 'جاري الاتصال بالمستشعر...';
                    statusElement.className = 'fingerprint-status scanning';

                    const connected = await connectFingerprintSensor(true); // فرض اتصال جديد
                    if (!connected) {
                        throw new Error('فشل في الاتصال بالمستشعر');
                    }
                }

                // إخفاء الأيقونة وإظهار الكانفاس
                iconElement.style.display = 'none';
                canvas.style.display = 'block';

                // بدء عملية الاستشعار الحقيقية
                await readRealFingerprint(type, canvas, statusElement);

            } catch (error) {
                console.error('خطأ في استشعار البصمة:', error);

                // عرض رسالة خطأ مفصلة
                let errorMessage = 'حدث خطأ: ';
                if (error.message.includes('لم يتم اختيار')) {
                    errorMessage += 'يجب اختيار مستشعر البصمة أولاً';
                } else if (error.message.includes('فشل في الاتصال')) {
                    errorMessage += 'تأكد من توصيل المستشعر بشكل صحيح';
                } else if (error.message.includes('انتهت مهلة')) {
                    errorMessage += 'انتهت مهلة الانتظار - تأكد من وضع الإصبع على المستشعر';
                } else {
                    errorMessage += error.message;
                }

                statusElement.textContent = errorMessage;
                statusElement.className = 'fingerprint-status error';
                retryButton.style.display = 'block';
                iconElement.style.display = 'block';
                canvas.style.display = 'none';

                // إعادة تعيين الاتصال في حالة خطأ الاتصال
                if (error.message.includes('الاتصال') || error.message.includes('المستشعر')) {
                    fingerprintPort = null;
                    sensorProtocol = null;
                    localStorage.removeItem('fingerprintDevice');
                }

            } finally {
                isScanning = false;
                startButton.disabled = false;
            }
        }

        // قراءة البصمة - نظام مبسط وقوي
        async function readRealFingerprint(type, canvas, statusElement) {
            const ctx = canvas.getContext('2d');
            const startButton = document.getElementById(`start-${type}-fingerprint`);
            const retryButton = document.getElementById(`retry-${type}-fingerprint`);

            try {
                statusElement.textContent = 'جاري تهيئة المستشعر...';
                statusElement.className = 'fingerprint-status scanning';

                // إرسال أوامر تهيئة متعددة
                await sendMultipleCommands();

                statusElement.textContent = 'ضع إصبعك على المستشعر والانتظار...';

                // بدء قراءة البيانات مع مهلة أطول
                const fingerprintData = await readFingerprintData(statusElement);

                if (!fingerprintData || fingerprintData.length === 0) {
                    throw new Error('لم يتم استلام أي بيانات من المستشعر');
                }

                statusElement.textContent = 'معالجة البيانات المستلمة...';

                // عرض البيانات المستلمة
                displayFingerprintData(ctx, fingerprintData);

                // حفظ البصمة
                const fingerprintDataURL = canvas.toDataURL();
                if (type === 'seller') {
                    sellerFingerprint = fingerprintDataURL;
                    document.getElementById('sellerFingerprintInput').value = fingerprintDataURL;
                } else {
                    buyerFingerprint = fingerprintDataURL;
                    document.getElementById('buyerFingerprintInput').value = fingerprintDataURL;
                }

                statusElement.textContent = `تم الحصول على البصمة ✓ (${fingerprintData.length} بايت)`;
                statusElement.className = 'fingerprint-status success';
                startButton.style.display = 'none';
                retryButton.style.display = 'block';

                console.log(`تم الحصول على بصمة ${type} - ${fingerprintData.length} بايت`);

            } catch (error) {
                console.error('خطأ في قراءة البصمة:', error);
                statusElement.textContent = 'خطأ: ' + error.message;
                statusElement.className = 'fingerprint-status error';
                retryButton.style.display = 'block';
                throw error;
            }
        }

        // إرسال أوامر متعددة للمستشعر
        async function sendMultipleCommands() {
            const writer = fingerprintPort.writable.getWriter();

            // قائمة أوامر شائعة لمستشعرات البصمة
            const commands = [
                // أوامر عامة
                [0x01],                                    // أمر بسيط
                [0x55, 0xAA],                             // header شائع
                [0x02, 0x00],                             // أمر تهيئة
                [0x01, 0x00, 0x00, 0x00],                 // أمر بدء

                // أوامر ZKTeco
                [0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x01, 0x00, 0x05],
                [0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x02, 0x00, 0x06],

                // أوامر Futronic
                [0x5A, 0xA5, 0x01, 0x00, 0x00, 0x00, 0x01],
                [0x5A, 0xA5, 0x02, 0x00, 0x00, 0x00, 0x02],

                // أوامر Digital Persona
                [0x02, 0x01, 0x00, 0x00, 0x00, 0x03],
                [0x02, 0x02, 0x00, 0x00, 0x00, 0x04],

                // أوامر إضافية
                [0x80, 0x01],                             // wake up
                [0x20, 0x01, 0x00],                       // scan command
                [0x30, 0x01],                             // get image
                [0x40],                                   // status
            ];

            try {
                console.log('إرسال أوامر متعددة للمستشعر...');

                for (let i = 0; i < commands.length; i++) {
                    try {
                        await writer.write(new Uint8Array(commands[i]));
                        console.log(`أمر ${i + 1}: ${commands[i].map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);

                        // انتظار قصير بين الأوامر
                        await new Promise(resolve => setTimeout(resolve, 100));
                    } catch (cmdError) {
                        console.log(`فشل في إرسال الأمر ${i + 1}:`, cmdError.message);
                    }
                }

                console.log('تم إرسال جميع الأوامر');

            } finally {
                writer.releaseLock();
            }
        }

        // قراءة بيانات البصمة - نظام مبسط
        async function readFingerprintData(statusElement) {
            const reader = fingerprintPort.readable.getReader();
            let allData = new Uint8Array();
            const maxWaitTime = 60000; // 60 ثانية
            const startTime = Date.now();
            let lastDataTime = Date.now();

            try {
                console.log('بدء قراءة البيانات من المستشعر...');
                statusElement.textContent = 'انتظار البيانات من المستشعر... (60 ثانية)';

                while (Date.now() - startTime < maxWaitTime) {
                    try {
                        // قراءة مع timeout قصير
                        const readPromise = reader.read();
                        const timeoutPromise = new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('timeout')), 1000)
                        );

                        const result = await Promise.race([readPromise, timeoutPromise]);

                        if (result.done) {
                            console.log('انتهت قراءة البيانات');
                            break;
                        }

                        if (result.value && result.value.length > 0) {
                            // دمج البيانات الجديدة
                            const newData = new Uint8Array(allData.length + result.value.length);
                            newData.set(allData);
                            newData.set(result.value, allData.length);
                            allData = newData;

                            lastDataTime = Date.now();

                            console.log(`استلام ${result.value.length} بايت، المجموع: ${allData.length}`);
                            console.log('البيانات الجديدة:', Array.from(result.value.slice(0, 10)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

                            // تحديث الحالة
                            const elapsed = Math.round((Date.now() - startTime) / 1000);
                            const remaining = Math.round((maxWaitTime - (Date.now() - startTime)) / 1000);
                            statusElement.textContent = `استلام البيانات... ${allData.length} بايت (${elapsed}s/${remaining}s متبقية)`;

                            // إذا لم تصل بيانات لمدة 5 ثوان والمجموع أكبر من 100 بايت، توقف
                            if (allData.length > 100 && (Date.now() - lastDataTime) > 5000) {
                                console.log('توقف استلام البيانات، انتهاء القراءة');
                                break;
                            }
                        }

                    } catch (readError) {
                        if (readError.message === 'timeout') {
                            // timeout عادي، استمرار المحاولة
                            const elapsed = Math.round((Date.now() - startTime) / 1000);
                            const remaining = Math.round((maxWaitTime - (Date.now() - startTime)) / 1000);

                            if (allData.length > 0) {
                                statusElement.textContent = `انتظار المزيد... ${allData.length} بايت (${remaining}s متبقية)`;
                            } else {
                                statusElement.textContent = `انتظار البيانات... (${remaining}s متبقية)`;
                            }
                            continue;
                        } else {
                            console.error('خطأ في القراءة:', readError);
                            break;
                        }
                    }
                }

                console.log(`انتهت القراءة - تم استلام ${allData.length} بايت`);

                if (allData.length === 0) {
                    throw new Error('لم يتم استلام أي بيانات خلال 60 ثانية');
                }

                return allData;

            } finally {
                try {
                    reader.releaseLock();
                } catch (error) {
                    console.log('تجاهل خطأ تحرير القارئ:', error.message);
                }
            }
        }

        // تم حذف الدوال القديمة المعقدة - النظام الآن مبسط وقوي

        function checkFingerprintComplete(data) {
            // التحقق من اكتمال البصمة حسب بروتوكول المستشعر
            // هذا مثال بسيط - يجب تعديله حسب نوع المستشعر المستخدم
            if (data.length < 8) return false;

            // البحث عن نهاية البيانات (مثال: 0xEF 0x01 في النهاية)
            const endMarker = [0xEF, 0x01];
            for (let i = data.length - 2; i >= 0; i--) {
                if (data[i] === endMarker[0] && data[i + 1] === endMarker[1]) {
                    return true;
                }
            }
            return false;
        }

        function processFingerprintData(rawData) {
            // معالجة البيانات الخام وتحويلها لمصفوفة صورة
            // هذا مثال بسيط - يجب تعديله حسب تنسيق بيانات المستشعر
            const imageSize = 200;
            const imageData = new Array(imageSize * imageSize);

            for (let i = 0; i < imageData.length; i++) {
                if (i < rawData.length) {
                    imageData[i] = rawData[i];
                } else {
                    imageData[i] = 0;
                }
            }

            return imageData;
        }

        // تم تبسيط النظام - لا حاجة لمعالجة معقدة

        // عرض بيانات البصمة - نظام مبسط وقوي
        function displayFingerprintData(ctx, rawData) {
            const canvasWidth = 200;
            const canvasHeight = 200;

            // مسح الكانفاس
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);

            if (!rawData || rawData.length === 0) {
                ctx.fillStyle = '#dc3545';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('لا توجد بيانات', canvasWidth/2, canvasHeight/2);
                return;
            }

            console.log(`عرض ${rawData.length} بايت من البيانات`);

            // تجربة طرق عرض مختلفة حسب حجم البيانات
            if (rawData.length >= 10000) {
                // بيانات كبيرة - محاولة عرضها كصورة
                displayAsImage(ctx, rawData, canvasWidth, canvasHeight);
            } else if (rawData.length >= 1000) {
                // بيانات متوسطة - عرضها كنقاط
                displayAsPoints(ctx, rawData, canvasWidth, canvasHeight);
            } else {
                // بيانات صغيرة - عرضها كرسم بياني
                displayAsGraph(ctx, rawData, canvasWidth, canvasHeight);
            }

            // إضافة معلومات البيانات
            ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
            ctx.fillRect(0, canvasHeight - 30, canvasWidth, 30);
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`البيانات: ${rawData.length} بايت`, 5, canvasHeight - 15);

            // عرض نوع العرض
            let displayType = '';
            if (rawData.length >= 10000) displayType = 'صورة';
            else if (rawData.length >= 1000) displayType = 'نقاط';
            else displayType = 'رسم بياني';

            ctx.fillText(`العرض: ${displayType}`, 5, canvasHeight - 5);

            // عرض إحصائيات
            const avg = rawData.reduce((sum, val) => sum + val, 0) / rawData.length;
            ctx.textAlign = 'right';
            ctx.fillText(`متوسط: ${Math.round(avg)}`, canvasWidth - 5, canvasHeight - 5);
        }

        // عرض البيانات كصورة
        function displayAsImage(ctx, data, width, height) {
            const imgData = ctx.createImageData(width, height);
            const totalPixels = width * height;

            // تحديد أبعاد الصورة المحتملة
            const possibleWidths = [256, 320, 355, 288, 480, 391];
            let bestWidth = Math.sqrt(data.length);

            // البحث عن أفضل عرض
            for (const w of possibleWidths) {
                if (data.length >= w * w * 0.8 && data.length <= w * w * 1.2) {
                    bestWidth = w;
                    break;
                }
            }

            const bestHeight = Math.floor(data.length / bestWidth);
            console.log(`عرض كصورة ${bestWidth}x${bestHeight}`);

            for (let i = 0; i < totalPixels; i++) {
                const x = i % width;
                const y = Math.floor(i / width);

                // حساب الموقع في البيانات الأصلية
                const srcX = Math.floor((x / width) * bestWidth);
                const srcY = Math.floor((y / height) * bestHeight);
                const srcIndex = srcY * bestWidth + srcX;

                let pixelValue = 128; // رمادي افتراضي
                if (srcIndex < data.length) {
                    pixelValue = data[srcIndex];
                }

                const pixelIndex = i * 4;
                imgData.data[pixelIndex] = pixelValue;     // Red
                imgData.data[pixelIndex + 1] = pixelValue; // Green
                imgData.data[pixelIndex + 2] = pixelValue; // Blue
                imgData.data[pixelIndex + 3] = 255;        // Alpha
            }

            ctx.putImageData(imgData, 0, 0);
        }

        // عرض البيانات كنقاط
        function displayAsPoints(ctx, data, width, height) {
            const pointsPerRow = Math.ceil(Math.sqrt(data.length));
            const pointWidth = width / pointsPerRow;
            const pointHeight = height / pointsPerRow;

            console.log(`عرض كنقاط ${pointsPerRow}x${pointsPerRow}`);

            for (let i = 0; i < data.length; i++) {
                const x = (i % pointsPerRow) * pointWidth;
                const y = Math.floor(i / pointsPerRow) * pointHeight;
                const intensity = data[i] / 255;

                if (intensity > 0.1) { // عتبة للنقاط المرئية
                    ctx.fillStyle = `rgba(0, 0, 0, ${intensity})`;
                    ctx.fillRect(x, y, Math.max(1, pointWidth), Math.max(1, pointHeight));
                }
            }
        }

        // عرض البيانات كرسم بياني
        function displayAsGraph(ctx, data, width, height) {
            console.log(`عرض كرسم بياني لـ ${data.length} نقطة`);

            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const step = width / data.length;

            for (let i = 0; i < data.length; i++) {
                const x = i * step;
                const y = height - (data[i] / 255) * height;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();

            // إضافة نقاط للتوضيح
            ctx.fillStyle = '#dc3545';
            for (let i = 0; i < Math.min(data.length, 20); i++) {
                const x = i * step;
                const y = height - (data[i] / 255) * height;
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            }
        }



        // تم حذف دوال المحاكاة - النظام يعمل مع المستشعرات الحقيقية فقط

        // إعادة محاولة استشعار البصمة
        function retryFingerprintScan(type) {
            const statusElement = document.getElementById(`${type}-fingerprint-status`);
            const startButton = document.getElementById(`start-${type}-fingerprint`);
            const retryButton = document.getElementById(`retry-${type}-fingerprint`);
            const canvas = document.getElementById(`${type}-fingerprint-canvas`);
            const iconElement = document.querySelector(`#${type}-fingerprint-preview .fingerprint-icon`);

            // إعادة تعيين الحالة
            statusElement.textContent = 'جاهز للاستشعار';
            statusElement.className = 'fingerprint-status waiting';

            startButton.style.display = 'block';
            retryButton.style.display = 'none';

            iconElement.style.display = 'block';
            canvas.style.display = 'none';

            // مسح الكانفاس
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, 200, 200);

            // مسح البيانات المحفوظة
            if (type === 'seller') {
                sellerFingerprint = null;
                document.getElementById('sellerFingerprintInput').value = '';
            } else {
                buyerFingerprint = null;
                document.getElementById('buyerFingerprintInput').value = '';
            }

            console.log(`تم إعادة تعيين بصمة ${type}`);
        }

        // إغلاق الاتصال بالمستشعر
        function closeFingerprintSensor() {
            try {
                // إيقاف مراقبة الاتصال
                if (connectionMonitor) {
                    clearInterval(connectionMonitor);
                    connectionMonitor = null;
                }

                // إغلاق المنفذ
                if (fingerprintPort && fingerprintPort.readable) {
                    fingerprintPort.close();
                    console.log('تم إغلاق الاتصال بمستشعر البصمة');
                }

                // إعادة تعيين المتغيرات
                fingerprintPort = null;
                sensorProtocol = null;

            } catch (error) {
                console.error('خطأ في إغلاق الاتصال:', error);
            }
        }

        // إغلاق الاتصال عند إغلاق الصفحة
        window.addEventListener('beforeunload', closeFingerprintSensor);

        // اختبار الاتصال الاحترافي بالمستشعر
        async function testFingerprintConnection() {
            const testBtn = document.getElementById('test-connection-btn');
            const statusSpan = document.getElementById('connection-status');

            testBtn.disabled = true;
            testBtn.textContent = '🔄 جاري الاختبار...';
            statusSpan.textContent = '';

            try {
                // إغلاق الاتصال السابق إن وجد
                if (fingerprintPort && fingerprintPort.readable) {
                    await fingerprintPort.close();
                    fingerprintPort = null;
                    sensorProtocol = null;
                }

                // محاولة الاتصال وكشف المستشعر
                statusSpan.innerHTML = '<span style="color: #007bff;">🔍 جاري كشف المستشعر...</span>';

                const connected = await connectFingerprintSensor(true); // فرض اتصال جديد

                if (connected && fingerprintPort && sensorProtocol) {
                    statusSpan.innerHTML = `<span style="color: #28a745;">✅ تم كشف مستشعر ${sensorProtocol.name}</span>`;

                    // اختبار التواصل مع المستشعر
                    try {
                        await initializeSensor();
                        statusSpan.innerHTML = `<span style="color: #28a745;">✅ ${sensorProtocol.name} جاهز للاستخدام</span>`;

                        // بدء مراقبة الاتصال
                        startConnectionMonitoring();

                        console.log(`تم اختبار المستشعر بنجاح: ${sensorProtocol.name}`);
                        console.log('معدل البيانات:', sensorProtocol.baudRate);
                        console.log('حجم الصورة:', sensorProtocol.imageSize);

                    } catch (initError) {
                        statusSpan.innerHTML = '<span style="color: #ffc107;">⚠️ تم الاتصال لكن فشل في التهيئة</span>';
                        console.error('خطأ في التهيئة:', initError);
                    }
                } else {
                    statusSpan.innerHTML = '<span style="color: #dc3545;">❌ لم يتم كشف أي مستشعر مدعوم</span>';
                }

            } catch (error) {
                let errorMessage = 'خطأ غير معروف';

                if (error.message.includes('لم يتم اختيار')) {
                    errorMessage = 'لم يتم اختيار أي جهاز';
                } else if (error.message.includes('Web Serial')) {
                    errorMessage = 'المتصفح لا يدعم Web Serial API';
                } else {
                    errorMessage = error.message;
                }

                statusSpan.innerHTML = `<span style="color: #dc3545;">❌ ${errorMessage}</span>`;
                console.error('خطأ في اختبار الاتصال:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🔌 اختبار الاتصال بالمستشعر';
            }
        }

        // دالة للتحقق من دعم المتصفح لمستشعرات البصمة
        function checkFingerprintSupport() {
            const supportInfo = document.createElement('div');
            supportInfo.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                color: #495057;
                z-index: 1000;
                max-width: 300px;
            `;

            let supportText = '<strong>دعم مستشعر البصمة:</strong><br>';

            if (navigator.serial) {
                supportText += '✅ Web Serial API مدعوم<br>';
            } else {
                supportText += '❌ Web Serial API غير مدعوم<br>';
            }

            if (navigator.usb) {
                supportText += '✅ WebUSB مدعوم<br>';
            } else {
                supportText += '❌ WebUSB غير مدعوم<br>';
            }

            if (!navigator.serial && !navigator.usb) {
                supportText += '<small style="color: #dc3545;">سيتم استخدام المحاكاة فقط</small>';
            } else {
                supportText += '<small style="color: #28a745;">يمكن استخدام مستشعر حقيقي</small>';
            }

            supportInfo.innerHTML = supportText;
            document.body.appendChild(supportInfo);

            // إخفاء الرسالة بعد 10 ثوان
            setTimeout(() => {
                if (supportInfo.parentNode) {
                    supportInfo.parentNode.removeChild(supportInfo);
                }
            }, 10000);
        }

        // استعادة الاتصال عند تحميل الصفحة - بدون طلب إذن
        async function restoreConnection() {
            try {
                // التحقق من دعم المتصفح
                if (!navigator.serial) {
                    console.log('المتصفح لا يدعم Web Serial API');
                    return;
                }

                const savedDevice = localStorage.getItem('fingerprintDevice');
                if (!savedDevice) {
                    console.log('لا يوجد جهاز محفوظ');
                    return;
                }

                const deviceInfo = JSON.parse(savedDevice);
                console.log('البحث عن الجهاز المحفوظ:', deviceInfo);

                // التحقق من صلاحية الجهاز المحفوظ (7 أيام)
                const weekInMs = 7 * 24 * 60 * 60 * 1000;
                if (Date.now() - deviceInfo.timestamp > weekInMs) {
                    console.log('انتهت صلاحية الجهاز المحفوظ');
                    localStorage.removeItem('fingerprintDevice');
                    return;
                }

                const statusSpan = document.getElementById('connection-status');

                // الحصول على قائمة المنافذ المتاحة (بدون طلب إذن جديد)
                const ports = await navigator.serial.getPorts();
                console.log(`تم العثور على ${ports.length} منفذ محفوظ`);

                for (const port of ports) {
                    const portInfo = port.getInfo();
                    console.log('فحص المنفذ:', portInfo);

                    if (portInfo.usbVendorId === deviceInfo.vendorId &&
                        portInfo.usbProductId === deviceInfo.productId) {

                        console.log('تم العثور على الجهاز المحفوظ');
                        if (statusSpan) {
                            statusSpan.innerHTML = '<span style="color: #007bff;">🔄 جاري الاتصال...</span>';
                        }

                        fingerprintPort = port;

                        // محاولة كشف نوع المستشعر
                        try {
                            sensorProtocol = await detectSensorType();

                            if (sensorProtocol) {
                                console.log(`تم استعادة الاتصال مع ${sensorProtocol.name}`);
                                if (statusSpan) {
                                    statusSpan.innerHTML = `<span style="color: #28a745;">✅ ${sensorProtocol.name} جاهز</span>`;
                                }
                                startConnectionMonitoring();
                                return;
                            }
                        } catch (detectError) {
                            console.log('فشل كشف المستشعر، استخدام البروتوكول الافتراضي');
                            sensorProtocol = SENSOR_PROTOCOLS.GENERIC;
                            if (statusSpan) {
                                statusSpan.innerHTML = '<span style="color: #ffc107;">⚠️ متصل (بروتوكول افتراضي)</span>';
                            }
                            return;
                        }
                    }
                }

                console.log('لم يتم العثور على الجهاز المحفوظ في المنافذ المتاحة');
                if (statusSpan) {
                    statusSpan.innerHTML = '<span style="color: #6c757d;">⏸️ الجهاز غير متصل</span>';
                }

            } catch (error) {
                console.error('خطأ في استعادة الاتصال:', error);
                const statusSpan = document.getElementById('connection-status');
                if (statusSpan) {
                    statusSpan.innerHTML = '<span style="color: #dc3545;">❌ خطأ في الاستعادة</span>';
                }
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من دعم المتصفح
            setTimeout(checkFingerprintSupport, 1000);

            // محاولة استعادة الاتصال
            setTimeout(restoreConnection, 2000);
        });


        function dataURItoBlob(dataURI) {
            let byteString = atob(dataURI.split(',')[1]);
            let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
            let arrayBuffer = new ArrayBuffer(byteString.length);
            let uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < byteString.length; i++) {
                uint8Array[i] = byteString.charCodeAt(i);
            }
            return new Blob([uint8Array], { type: mimeString });
        }

        // التحقق من صحة النموذج قبل الإرسال
        function validateForm() {
            // قائمة الحقول المطلوبة
            const requiredFields = [
                { id: 'name_1', name: 'اسم المالك الشرعي' },
                { id: 'location_1', name: 'عنوان المالك الشرعي' },
                { id: 'name_2', name: 'اسم البائع' },
                { id: 'location_2', name: 'عنوان البائع' },
                { id: 'id_1', name: 'رقم الهوية للبائع' },
                { id: 'phone_1', name: 'رقم الهاتف للبائع' },
                { id: 'name_3', name: 'اسم المشتري' },
                { id: 'location_3', name: 'عنوان المشتري' },
                { id: 'id_2', name: 'رقم هوية المشتري' },
                { id: 'phone_2', name: 'رقم الهاتف للمشتري' },
                { id: 'car_type', name: 'نوع السيارة' },
                { id: 'car_num', name: 'رقم السيارة' },
                { id: 'car_city', name: 'المدينة' },
                { id: 'car_prop', name: 'نوع السيارة' },
                { id: 'sin_num', name: 'رقم السنوية' },
                { id: 'car_colar', name: 'لون السيارة' },
                { id: 'car_model', name: 'موديل السيارة' },
                { id: 'sasi_num', name: 'رقم الشاصي' },
                { id: 'currency_type', name: 'نوع العملة' },
                { id: 'badal_num', name: 'قيمة البيع' },
                { id: 'mony_num', name: 'المبلغ المقبوض' },
                { id: 't', name: 'الوقت' },
                { id: 't_1', name: 'الفترة' },
                { id: 'day', name: 'اليوم' }
            ];

            // التحقق من جميع الحقول المطلوبة
            for (let field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element || !element.value.trim()) {
                    alert(`يرجى تعبئة حقل: ${field.name}`);
                    if (element) element.focus();
                    return false;
                }
            }

            // التحقق من أرقام الهاتف
            const phone1 = document.getElementById('phone_1').value.replace(/[^0-9]/g, '');
            const phone2 = document.getElementById('phone_2').value.replace(/[^0-9]/g, '');

            if (phone1.length !== 11) {
                alert('رقم هاتف البائع يجب أن يكون 11 رقم بالضبط');
                document.getElementById('phone_1').focus();
                return false;
            }

            if (phone2.length !== 11) {
                alert('رقم هاتف المشتري يجب أن يكون 11 رقم بالضبط');
                document.getElementById('phone_2').focus();
                return false;
            }

            // التحقق من وجود الصور
            if (!buyerPhoto) {
                alert('يرجى التقاط صورة البائع');
                return false;
            }

            if (!sellerPhoto) {
                alert('يرجى التقاط صورة المشتري');
                return false;
            }

            // التحقق من البصمات
            if (!sellerFingerprint) {
                alert('يرجى الحصول على بصمة البائع');
                return false;
            }

            if (!buyerFingerprint) {
                alert('يرجى الحصول على بصمة المشتري');
                return false;
            }

            return true;
        }

        // التحقق من طول رقم الهاتف
        function validatePhoneNumber(phoneId, warningId) {
            const phoneInput = document.getElementById(phoneId);
            const warningDiv = document.getElementById(warningId);

            phoneInput.addEventListener('input', function() {
                const phoneValue = this.value.replace(/[^0-9]/g, ''); // إزالة أي شيء غير الأرقام
                this.value = phoneValue; // تحديث القيمة لتحتوي على الأرقام فقط

                if (phoneValue.length !== 11) {
                    warningDiv.style.display = 'block';
                    this.style.borderColor = 'red';
                } else {
                    warningDiv.style.display = 'none';
                    this.style.borderColor = '#000';
                }
            });

            // التحقق عند فقدان التركيز
            phoneInput.addEventListener('blur', function() {
                const phoneValue = this.value.replace(/[^0-9]/g, '');
                if (phoneValue.length !== 11) {
                    warningDiv.style.display = 'block';
                    this.style.borderColor = 'red';
                }
            });
        }
        
    </script>

    <!-- تضمين وحدة تحويل الأرقام العربية المحسنة -->
    <script src="{{ url_for('static', filename='arabic_number_converter.js') }}"></script>

    <script>
        // دوال مساعدة للتوافق مع الكود الموجود - تستخدم الوحدة المحسنة
        function numberToArabicText(number, currencyType) {
            try {
                return convertNumberToArabicText(number, currencyType);
            } catch (error) {
                console.error('خطأ في تحويل الرقم:', error);
                // إرجاع نص افتراضي في حالة الخطأ
                if (currencyType === "USD") {
                    return "صفر دولار أمريكي";
                } else {
                    return "صفر دينار عراقي";
                }
            }
        }

        // دالة تنسيق الرقم بالفواصل والعلامة النقدية - محسنة
        function formatNumberWithCurrency(number, currencyType) {
            try {
                return arabicConverter.formatNumberWithCurrency(number, currencyType);
            } catch (error) {
                console.error('خطأ في تنسيق الرقم:', error);
                return "0";
            }
        }

        // دالة تحديث عرض المبلغ
        function updateAmountDisplay(inputId, displayId) {
            const input = document.getElementById(inputId);
            const display = document.getElementById(displayId);
            const currencyType = document.getElementById('currency_type').value;

            const value = input.value.replace(/[^0-9]/g, '');

            if (value) {
                const number = parseInt(value);
                const formattedNumber = formatNumberWithCurrency(number, currencyType);
                const arabicText = numberToArabicText(number, currencyType);

                display.innerHTML = `
                    <div class="formatted-number">${formattedNumber}</div>
                    <div class="arabic-text">${arabicText}</div>
                `;
            } else {
                display.innerHTML = "";
            }

            // حساب المبلغ المتبقي
            calculateRemainingAmount();
        }

        // دالة حساب المبلغ المتبقي
        function calculateRemainingAmount() {
            const badalValue = document.getElementById('badal_num').value.replace(/[^0-9]/g, '');
            const monyValue = document.getElementById('mony_num').value.replace(/[^0-9]/g, '');
            const currencyType = document.getElementById('currency_type').value;

            const badalAmount = parseInt(badalValue) || 0;
            const monyAmount = parseInt(monyValue) || 0;
            const remainingAmount = badalAmount - monyAmount;

            // تحديث الحقل المخفي
            document.getElementById('mony_not_delevired').value = remainingAmount;

            // تحديث حقل العرض
            const displayField = document.getElementById('mony_not_delevired_display_field');
            const displayDiv = document.getElementById('mony_not_delevired_display');

            // عرض المبلغ المتبقي دائماً (حتى لو كان صفر)
            const formattedNumber = formatNumberWithCurrency(remainingAmount, currencyType);
            const arabicText = numberToArabicText(remainingAmount, currencyType);

            displayField.value = formattedNumber;
            displayDiv.innerHTML = `
                <div class="formatted-number">${formattedNumber}</div>
                <div class="arabic-text">${arabicText}</div>
            `;

            // إدارة حقل ملاحظات الباقي
            const m1Field = document.getElementById('m1');
            const remainingNotesRow = document.getElementById('remaining_notes_row');

            if (remainingAmount > 0) {
                // إظهار الحقل إذا كان الباقي أكبر من 0
                remainingNotesRow.style.display = 'block';
                m1Field.disabled = false;
            } else {
                // إخفاء الحقل وإفراغه إذا كان الباقي = 0
                remainingNotesRow.style.display = 'none';
                m1Field.value = '';
                m1Field.disabled = true;
            }
        }

        // متغيرات إدارة العقود
        let isEditingMode = false;
        let savedContractData = null;

        window.onload = function() {
            startVideo('buyer');
            startVideo('seller');

            // تطبيق التحقق على حقول رقم الهاتف
            validatePhoneNumber('phone_1', 'phone_1_warning');
            validatePhoneNumber('phone_2', 'phone_2_warning');

            // ربط أحداث تحديث المبالغ
            const badalInput = document.getElementById('badal_num');
            const monyInput = document.getElementById('mony_num');
            const currencySelect = document.getElementById('currency_type');

            badalInput.addEventListener('input', function() {
                updateAmountDisplay('badal_num', 'badal_num_display');
            });

            monyInput.addEventListener('input', function() {
                updateAmountDisplay('mony_num', 'mony_num_display');
            });

            // تحميل الرقم التسلسلي الحالي
            updateCurrentSerial();

            // التحقق من وجود عقد محفوظ
            checkSavedContract();

            // تحديث الرقم التسلسلي كل 10 دقائق مع مراقبة النشاط
            startSmartSerialUpdate();

            currencySelect.addEventListener('change', function() {
                updateAmountDisplay('badal_num', 'badal_num_display');
                updateAmountDisplay('mony_num', 'mony_num_display');
                calculateRemainingAmount(); // تحديث المبلغ المتبقي عند تغيير العملة
            });

            // تحديث أولي للمبلغ المتبقي
            calculateRemainingAmount();
        };

        // متغيرات مراقبة النشاط للصفحة الرئيسية
        let serialUpdateInterval = null;
        let lastUserActivity = Date.now();
        let isMainPageVisible = true;

        // تحديث الرقم التسلسلي الحالي مع تحسينات الأداء
        function updateCurrentSerial() {
            // تجاهل التحديث إذا كانت الصفحة غير مرئية أو غير نشطة لأكثر من 15 دقيقة
            if (!isMainPageVisible || (Date.now() - lastUserActivity > 900000)) { // 15 دقيقة عدم نشاط
                console.log('تم تجاهل تحديث الرقم التسلسلي - الصفحة غير نشطة');
                return;
            }

            fetch('/api/current_serial')
                .then(response => response.json())
                .then(data => {
                    if (data.current_serial) {
                        document.getElementById('current-serial-display').textContent = data.current_serial;
                    }
                })
                .catch(error => {
                    console.error('خطأ في جلب الرقم التسلسلي:', error);
                });
        }

        // بدء التحديث الذكي للرقم التسلسلي
        function startSmartSerialUpdate() {
            // تحديث كل 10 دقائق بدلاً من 5 دقائق لتوفير الموارد
            serialUpdateInterval = setInterval(updateCurrentSerial, 600000);

            // مراقبة نشاط المستخدم
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click', 'focus'].forEach(event => {
                document.addEventListener(event, function() {
                    lastUserActivity = Date.now();
                }, true);
            });

            // مراقبة رؤية الصفحة
            document.addEventListener('visibilitychange', function() {
                isMainPageVisible = !document.hidden;
                if (isMainPageVisible) {
                    lastUserActivity = Date.now();
                    updateCurrentSerial(); // تحديث فوري عند العودة للصفحة
                }
            });
        }

        // التحقق من وجود عقد محفوظ
        function checkSavedContract() {
            fetch('/api/saved_contract')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.contract) {
                        savedContractData = data.contract;
                        document.getElementById('load-saved-btn').style.display = 'inline-block';
                        document.getElementById('last-save-date').textContent = data.contract.contract_info.last_modified;
                    } else {
                        document.getElementById('load-saved-btn').style.display = 'none';
                        document.getElementById('last-save-date').textContent = 'لا يوجد';
                    }
                })
                .catch(error => {
                    console.error('خطأ في التحقق من العقد المحفوظ:', error);
                });
        }

        // تحميل العقد المحفوظ
        function loadSavedContract() {
            if (!savedContractData) {
                alert('لا يوجد عقد محفوظ للتحميل');
                return;
            }

            const contractSerial = savedContractData.contract_info.serial_number;
            const hasImages = savedContractData.images && (savedContractData.images.buyerPhoto || savedContractData.images.sellerPhoto);

            let confirmMessage = `هل تريد تحميل العقد المحفوظ رقم ${contractSerial}؟\n\nسيتم استبدال البيانات الحالية وعند إنشاء العقد سيحتفظ بنفس الرقم التسلسلي.`;

            if (hasImages) {
                confirmMessage += `\n\n📷 العقد يحتوي على صور محفوظة وستتم استعادتها.`;
            }

            if (confirm(confirmMessage)) {
                const formData = savedContractData.form_data;

                // ملء جميع حقول النموذج
                for (const [key, value] of Object.entries(formData)) {
                    const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
                    if (element) {
                        element.value = value;

                        // تحديث عرض المبالغ إذا كان الحقل مبلغ
                        if (key === 'badal_num') {
                            updateAmountDisplay('badal_num', 'badal_num_display');
                        } else if (key === 'mony_num') {
                            updateAmountDisplay('mony_num', 'mony_num_display');
                        }
                    }
                }

                // استعادة الصور إذا كانت موجودة
                if (savedContractData.images) {
                    if (savedContractData.images.buyerPhoto) {
                        buyerPhoto = savedContractData.images.buyerPhoto;
                        document.getElementById('buyerPhotoInput').value = buyerPhoto;

                        // عرض الصورة
                        const photoBox = document.getElementById('photo-box-buyer');
                        const videoElement = document.getElementById('video-buyer');
                        photoBox.innerHTML = `<img src="data:image/jpeg;base64,${buyerPhoto}" alt="Buyer Photo">`;
                        photoBox.style.display = 'block';
                        videoElement.style.display = 'none';
                        document.getElementById('capture-btn-buyer').style.display = 'none';
                        document.getElementById('retake-btn-buyer').style.display = 'inline-block';
                    }

                    if (savedContractData.images.sellerPhoto) {
                        sellerPhoto = savedContractData.images.sellerPhoto;
                        document.getElementById('sellerPhotoInput').value = sellerPhoto;

                        // عرض الصورة
                        const photoBox = document.getElementById('photo-box-seller');
                        const videoElement = document.getElementById('video-seller');
                        photoBox.innerHTML = `<img src="data:image/jpeg;base64,${sellerPhoto}" alt="Seller Photo">`;
                        photoBox.style.display = 'block';
                        videoElement.style.display = 'none';
                        document.getElementById('capture-btn-seller').style.display = 'none';
                        document.getElementById('retake-btn-seller').style.display = 'inline-block';
                    }
                }

                // تحديث حالة العقد
                isEditingMode = true;
                updateContractStatus(`تعديل عقد محفوظ (رقم ${contractSerial})`, contractSerial);
                document.getElementById('is_editing_saved').value = 'true';



                let successMessage = `تم تحميل العقد رقم ${contractSerial} بنجاح\n\nيمكنك الآن تعديل البيانات وإنشاء العقد بنفس الرقم التسلسلي.`;

                if (hasImages) {
                    successMessage += `\n\n📷 تم استعادة الصور المحفوظة أيضاً.`;
                }

                alert(successMessage);
            }
        }

        // بدء عقد جديد
        function startNewContract() {
            if (confirm('هل تريد بدء عقد جديد؟\n\nسيتم مسح جميع البيانات الحالية والصور وسيحصل العقد الجديد على رقم تسلسلي جديد.')) {
                // مسح جميع حقول النموذج
                const form = document.querySelector('form');
                const inputs = form.querySelectorAll('input[type="text"], input[type="number"], select, textarea');
                inputs.forEach(input => {
                    if (input.type !== 'hidden') {
                        input.value = '';
                    }
                });

                // مسح عرض المبالغ
                document.getElementById('badal_num_display').textContent = '';
                document.getElementById('mony_num_display').textContent = '';

                // مسح الصور
                buyerPhoto = null;
                sellerPhoto = null;
                document.getElementById('buyerPhotoInput').value = '';
                document.getElementById('sellerPhotoInput').value = '';

                // إخفاء صور البائع والمشتري وإظهار الكاميرا
                const buyerPhotoBox = document.getElementById('photo-box-buyer');
                const sellerPhotoBox = document.getElementById('photo-box-seller');
                const buyerVideo = document.getElementById('video-buyer');
                const sellerVideo = document.getElementById('video-seller');

                if (buyerPhotoBox) {
                    buyerPhotoBox.style.display = 'none';
                    buyerPhotoBox.innerHTML = '';
                }
                if (sellerPhotoBox) {
                    sellerPhotoBox.style.display = 'none';
                    sellerPhotoBox.innerHTML = '';
                }
                if (buyerVideo) buyerVideo.style.display = 'block';
                if (sellerVideo) sellerVideo.style.display = 'block';

                // إظهار أزرار التقاط الصور وإخفاء أزرار إعادة التقاط
                const captureButtons = document.querySelectorAll('[id*="capture-btn"]');
                const retakeButtons = document.querySelectorAll('[id*="retake-btn"]');
                captureButtons.forEach(btn => btn.style.display = 'inline-block');
                retakeButtons.forEach(btn => btn.style.display = 'none');

                // إعادة تعيين حالة العقد
                isEditingMode = false;
                updateContractStatus('عقد جديد', null);
                document.getElementById('is_editing_saved').value = 'false';
                document.getElementById('local_serial_number').value = '';



                alert('تم بدء عقد جديد\n\nسيحصل العقد على رقم تسلسلي جديد عند الإنشاء وسيتم حفظه تلقائياً كمسودة.');
            }
        }

        // دالة إبرام العقد الجديدة مع نظام PDF المحسن
        function finalizeContract() {
            // التحقق من صحة النموذج أولاً
            if (!validateForm()) {
                return;
            }

            // إظهار رسالة التحميل
            const loadingMessage = document.createElement('div');
            loadingMessage.id = 'loading-message';
            loadingMessage.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8); color: white; padding: 20px 30px;
                border-radius: 10px; z-index: 9999; text-align: center;
                font-size: 16px; font-weight: bold;
            `;
            loadingMessage.innerHTML = `
                <div>🔄 جاري إبرام العقد وتحويله إلى PDF...</div>
                <div style="margin-top: 10px; font-size: 14px; color: #ccc;">يرجى الانتظار، قد تستغرق العملية دقيقة أو أكثر</div>
            `;
            document.body.appendChild(loadingMessage);

            try {
                // جمع بيانات النموذج
                const formData = new FormData();
                const form = document.querySelector('form[action="/"]');
                const inputs = form.querySelectorAll('input, select, textarea');

                inputs.forEach(input => {
                    if (input.name) {
                        formData.append(input.name, input.value);
                    }
                });

                // إضافة الصور
                if (buyerPhoto) {
                    formData.append('buyerPhoto', buyerPhoto);
                }
                if (sellerPhoto) {
                    formData.append('sellerPhoto', sellerPhoto);
                }

                // إرسال الطلب لإبرام العقد مع تحويل PDF
                fetch('/api/finalize_contract', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // إزالة رسالة التحميل
                    const loadingMsg = document.getElementById('loading-message');
                    if (loadingMsg) {
                        loadingMsg.remove();
                    }

                    if (data.success && data.pdf_converted) {
                        // نجح تحويل PDF - فتح ملف PDF مباشرة في المتصفح بدون رسائل منبثقة

                        // فتح ملف PDF مباشرة في تبويب جديد
                        const pdfUrl = `/pdf/${data.pdf_filename}`;
                        window.open(pdfUrl, '_blank');

                    } else if (!data.success && data.redirect_to_edit) {
                        // فشل تحويل PDF - توجيه للتعديل
                        alert(`❌ تم حفظ العقد رقم ${data.serial_number} ولكن فشل في تحويله إلى PDF.\n\n${data.error_details}\n\nسيتم توجيهك لصفحة التعديل لإعادة المحاولة.`);

                        // تحميل العقد المحفوظ للتعديل
                        setTimeout(() => {
                            checkSavedContract();
                            loadSavedContract();
                        }, 1000);

                    } else {
                        // خطأ عام
                        const errorMessage = data.error || 'خطأ غير معروف';
                        console.error('خطأ في إبرام العقد:', data);
                        alert('❌ خطأ في إبرام العقد: ' + errorMessage);
                    }
                })
                .catch(error => {
                    // إزالة رسالة التحميل
                    const loadingMsg = document.getElementById('loading-message');
                    if (loadingMsg) {
                        loadingMsg.remove();
                    }

                    console.error('خطأ في إبرام العقد:', error);

                    // تحديد نوع الخطأ وإظهار رسالة مناسبة
                    let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                    if (error.name === 'TypeError' && error.message.includes('fetch')) {
                        errorMessage = 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت';
                    } else if (error.name === 'AbortError') {
                        errorMessage = 'تم إلغاء العملية بسبب انتهاء المهلة الزمنية';
                    } else if (error.message) {
                        errorMessage = error.message;
                    }

                    alert('❌ ' + errorMessage);
                });

            } catch (error) {
                // إزالة رسالة التحميل
                const loadingMsg = document.getElementById('loading-message');
                if (loadingMsg) {
                    loadingMsg.remove();
                }

                console.error('خطأ في إبرام العقد:', error);
                alert('❌ حدث خطأ في إبرام العقد: ' + error.message);
            }
        }

        // جمع بيانات النموذج
        function collectFormData() {
            const formData = {};
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                if (input.name && input.name !== 'buyerPhoto' && input.name !== 'sellerPhoto') {
                    // تضمين جميع الحقول بما في ذلك المخفية المطلوبة
                    if (input.type !== 'hidden' || input.name === 'is_editing_saved' || input.name === 'local_serial_number' || input.name === 'manual_serial_number') {
                        formData[input.name] = input.value;
                    }
                }
            });

            return formData;
        }

        // تحديث حالة العقد بعد إنشاء مستند Word
        function updateContractStatusAfterSubmit() {
            // تحديث حالة العقد لتظهر أنه تم حفظه
            setTimeout(() => {
                checkSavedContract();
                document.getElementById('contract-mode').textContent = 'عقد محفوظ (يمكن تعديله)';

                // إظهار رسالة تأكيد
                const statusDiv = document.getElementById('contract-status');
                const successMsg = document.createElement('div');
                successMsg.style.cssText = 'margin-top: 10px; padding: 8px; background-color: #d4edda; border-radius: 5px; border: 1px solid #c3e6cb; color: #155724;';
                successMsg.innerHTML = '<strong>✅ تم حفظ العقد تلقائياً كمسودة - يمكنك تعديله لاحقاً</strong>';

                // إزالة الرسالة السابقة إن وجدت
                const existingMsg = statusDiv.querySelector('.success-message');
                if (existingMsg) {
                    existingMsg.remove();
                }

                successMsg.className = 'success-message';
                statusDiv.appendChild(successMsg);

                // إخفاء الرسالة بعد 5 ثوانٍ
                setTimeout(() => {
                    if (successMsg.parentNode) {
                        successMsg.remove();
                    }
                }, 5000);
            }, 1000);
        }

        // دوال عداد الأحرف والتحذيرات
        function setupCharacterCounter(fieldId, maxChars, warningMessage) {
            const field = document.getElementById(fieldId);
            if (!field) return;

            // إنشاء عنصر العداد
            const counterDiv = document.createElement('div');
            counterDiv.id = fieldId + '_counter';
            counterDiv.className = 'char-counter';

            // إنشاء عنصر التحذير
            const warningDiv = document.createElement('div');
            warningDiv.id = fieldId + '_warning';
            warningDiv.className = 'char-warning';
            warningDiv.textContent = warningMessage;

            // إدراج العناصر بعد الحقل
            field.parentNode.insertBefore(counterDiv, field.nextSibling);
            field.parentNode.insertBefore(warningDiv, counterDiv.nextSibling);

            // دالة تحديث العداد
            function updateCounter() {
                const currentLength = field.value.length;
                const remaining = maxChars - currentLength;

                // تحديث نص العداد
                counterDiv.textContent = `${currentLength}/${maxChars} حرف`;

                // تحديث ألوان العداد
                counterDiv.classList.remove('safe', 'warning', 'danger');
                if (currentLength <= maxChars * 0.7) {
                    counterDiv.classList.add('safe');
                } else if (currentLength <= maxChars) {
                    counterDiv.classList.add('warning');
                } else {
                    counterDiv.classList.add('danger');
                }

                // إظهار/إخفاء التحذير
                if (currentLength > maxChars) {
                    warningDiv.classList.add('show', 'danger');
                } else {
                    warningDiv.classList.remove('show', 'danger');
                }
            }

            // ربط الأحداث
            field.addEventListener('input', updateCounter);
            field.addEventListener('keyup', updateCounter);
            field.addEventListener('paste', () => setTimeout(updateCounter, 10));

            // تحديث أولي
            updateCounter();
        }

        // دالة التوسع التلقائي للـ textarea
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        }

        // إعداد التوسع التلقائي لجميع textarea
        function setupAutoResize() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                // تطبيق التوسع عند الكتابة
                textarea.addEventListener('input', function() {
                    autoResize(this);
                });

                // تطبيق التوسع عند اللصق
                textarea.addEventListener('paste', function() {
                    setTimeout(() => autoResize(this), 10);
                });

                // تطبيق التوسع الأولي
                autoResize(textarea);
            });
        }

        // تطبيق العدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إعداد التوسع التلقائي
            setupAutoResize();

            // عداد ملاحظة أ (120 حرف)
            setupCharacterCounter('note_a', 120, '⚠️ تجاوز الحد المسموح (120 حرف) - قد لا تظهر كاملة في الخلية');

            // عداد ملاحظة ب (300 حرف)
            setupCharacterCounter('note_b', 300, '⚠️ تجاوز الحد المسموح (300 حرف) - قد لا تظهر كاملة في الخلية');

            // عداد ملاحظات الباقي (70 حرف)
            setupCharacterCounter('m1', 70, '⚠️ تجاوز الحد المسموح (70 حرف) - قد لا تظهر كاملة في الخلية');
        });

        // تحديث دالة إرسال النموذج
        function enhanceFormSubmission() {
            const form = document.querySelector('form[action="/"]');
            if (form) {
                form.addEventListener('submit', function(event) {
                    // السماح بإرسال النموذج بشكل طبيعي
                    // ولكن تحديث الحالة بعد ذلك
                    updateContractStatusAfterSubmit();
                });
            }
        }

        // تشغيل تحسين إرسال النموذج عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            enhanceFormSubmission();
        });





        // دالة تحديث حالة العقد
        function updateContractStatus(mode, serialNumber = null) {
            document.getElementById('contract-mode').textContent = mode;

            if (serialNumber) {
                document.getElementById('draft-serial-display').textContent = serialNumber;
                document.getElementById('draft-serial-display').style.color = '#28a745';
            } else {
                document.getElementById('draft-serial-display').textContent = 'لا يوجد';
                document.getElementById('draft-serial-display').style.color = '#6c757d';
            }

            // تحديث تاريخ آخر حفظ
            document.getElementById('last-save-date').textContent = new Date().toLocaleString('ar-EG');
        }













        // تحديث دالة التحميل الأولي
        document.addEventListener('DOMContentLoaded', function() {
            enhanceFormSubmission();
        });
    </script>
        </div>
    </div>

</body>
</html>
